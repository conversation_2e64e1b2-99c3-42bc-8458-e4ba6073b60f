import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';

interface UserData {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  isAuthenticated: boolean;
}

interface AuthContextType {
  user: UserData | null;
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: RegisterData) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  saveUserData: (data: UserAppData) => void;
  getUserData: () => UserAppData | null;
  clearUserData: () => void;
  isNewUser: boolean;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  avatar?: string;
}

interface StoredUser {
  id: string;
  username: string;
  email: string;
  password: string;
  avatar?: string;
}

interface UserAppData {
  // Patient Details
  patientName: string;
  complaint: string;
  appointmentType: 'Initial' | 'Follow-Up' | 'Consultation';
  appointmentDate: string;
  appointmentTime: string;
  consents: boolean[];

  // Chat History
  chatHistory: Array<{ role: 'user' | 'assistant'; content: string; timestamp: string }>;

  // Additional user preferences or data
  lastLoginDate: string;
  totalSessions: number;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isNewUser, setIsNewUser] = useState(false);

  // Check for existing user session on app load
  useEffect(() => {
    const checkExistingSession = () => {
      try {
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
          const userData = JSON.parse(currentUser);
          setUser({
            id: userData.id,
            username: userData.username,
            email: userData.email,
            avatar: userData.avatar,
            isAuthenticated: true,
          });
          // If loading existing session, this is not a new user
          setIsNewUser(false);
        }
      } catch (error) {
        console.error('Error loading user session:', error);
        localStorage.removeItem('currentUser');
      } finally {
        setIsLoading(false);
      }
    };

    checkExistingSession();
  }, []);

  const register = async (userData: RegisterData): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Get existing users from localStorage
      const existingUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]') as StoredUser[];

      // Check if email already exists
      const existingUser = existingUsers.find(user => user.email === userData.email);

      if (existingUser) {
        // If email exists, check if password matches
        if (existingUser.password === userData.password) {
          // Same email and password - log them in automatically (returning user)
          setIsNewUser(false);

          const userSession = {
            id: existingUser.id,
            username: existingUser.username,
            email: existingUser.email,
            avatar: existingUser.avatar,
          };
          localStorage.setItem('currentUser', JSON.stringify(userSession));

          // Update state
          setUser({
            ...userSession,
            isAuthenticated: true,
          });

          // Increment session count for returning users
          setTimeout(() => {
            const userDataKey = `userData_${existingUser.id}`;
            const existingUserData = localStorage.getItem(userDataKey);
            if (existingUserData) {
              try {
                const parsedData = JSON.parse(existingUserData);
                parsedData.totalSessions = (parsedData.totalSessions || 1) + 1;
                parsedData.lastLoginDate = new Date().toISOString();
                localStorage.setItem(userDataKey, JSON.stringify(parsedData));
              } catch (error) {
                console.error('Error updating session count:', error);
              }
            }
          }, 100);

          return true;
        } else {
          // Same email but different password
          alert('Email already registered with a different password. Please use the correct password or try a different email.');
          return false;
        }
      }

      // Check if username already exists (only for new registrations)
      const usernameExists = existingUsers.some(user => user.username === userData.username);
      if (usernameExists) {
        alert('Username already taken. Please choose a different username.');
        return false;
      }

      // Create new user (brand new registration)
      setIsNewUser(true);

      const newUser: StoredUser = {
        id: Date.now().toString(),
        username: userData.username,
        email: userData.email,
        password: userData.password, // In a real app, this should be hashed
        avatar: userData.avatar,
      };

      // Save to localStorage
      const updatedUsers = [...existingUsers, newUser];
      localStorage.setItem('registeredUsers', JSON.stringify(updatedUsers));

      // Set current user session
      const userSession = {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        avatar: newUser.avatar,
      };
      localStorage.setItem('currentUser', JSON.stringify(userSession));

      // Ensure no existing data for this new user (clean slate)
      const userDataKey = `userData_${newUser.id}`;
      localStorage.removeItem(userDataKey);

      // Update state
      setUser({
        ...userSession,
        isAuthenticated: true,
      });

      return true;
    } catch (error) {
      console.error('Registration error:', error);
      alert('Registration failed. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Get existing users from localStorage
      const existingUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]') as StoredUser[];

      // Find user by email and password
      const foundUser = existingUsers.find(user =>
        user.email === email && user.password === password
      );

      if (!foundUser) {
        alert('Invalid email or password. Please try again.');
        return false;
      }

      // This is a returning user login
      setIsNewUser(false);

      // Set current user session
      const userSession = {
        id: foundUser.id,
        username: foundUser.username,
        email: foundUser.email,
        avatar: foundUser.avatar,
      };
      localStorage.setItem('currentUser', JSON.stringify(userSession));

      // Update state
      setUser({
        ...userSession,
        isAuthenticated: true,
      });

      // Increment session count for returning users
      setTimeout(() => {
        const userDataKey = `userData_${foundUser.id}`;
        const existingUserData = localStorage.getItem(userDataKey);
        if (existingUserData) {
          try {
            const parsedData = JSON.parse(existingUserData);
            parsedData.totalSessions = (parsedData.totalSessions || 1) + 1;
            parsedData.lastLoginDate = new Date().toISOString();
            localStorage.setItem(userDataKey, JSON.stringify(parsedData));
          } catch (error) {
            console.error('Error updating session count:', error);
          }
        }
      }, 100);

      return true;
    } catch (error) {
      console.error('Login error:', error);
      alert('Login failed. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    console.log('AuthContext: Starting complete logout process...');
    try {
      // Clear current user session from localStorage
      localStorage.removeItem('currentUser');
      console.log('AuthContext: Removed currentUser from localStorage');

      // Reset all user state to initial values
      setUser(null);
      setIsNewUser(false);
      setIsLoading(false);

      console.log('AuthContext: All user state reset to initial values');
      console.log('AuthContext: Complete logout successful - user must login fresh');
    } catch (error) {
      console.error('AuthContext: Error during logout:', error);
      // Even if there's an error, ensure user state is cleared
      setUser(null);
      setIsNewUser(false);
      setIsLoading(false);
    }
  };

  const saveUserData = (data: UserAppData) => {
    if (!user) return;

    try {
      // Save user-specific data with user ID as key
      const userDataKey = `userData_${user.id}`;
      const dataToSave = {
        ...data,
        lastLoginDate: new Date().toISOString(),
      };
      localStorage.setItem(userDataKey, JSON.stringify(dataToSave));
    } catch (error) {
      console.error('Error saving user data:', error);
    }
  };

  const getUserData = (): UserAppData | null => {
    if (!user) return null;

    try {
      const userDataKey = `userData_${user.id}`;
      const savedData = localStorage.getItem(userDataKey);

      if (savedData) {
        const parsedData = JSON.parse(savedData);
        return parsedData;
      }

      // Return null if no saved data exists (new user)
      return null;
    } catch (error) {
      console.error('Error loading user data:', error);
      return null;
    }
  };

  const clearUserData = () => {
    if (!user) return;

    try {
      const userDataKey = `userData_${user.id}`;
      localStorage.removeItem(userDataKey);
    } catch (error) {
      console.error('Error clearing user data:', error);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      register,
      logout,
      isLoading,
      saveUserData,
      getUserData,
      clearUserData,
      isNewUser
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
