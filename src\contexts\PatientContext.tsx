import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { useAuth } from './AuthContext';

type AppointmentType = 'Initial' | 'Follow-Up' | 'Consultation';

interface PatientContextType {
  patientName: string;
  setPatientName: (name: string) => void;
  complaint: string;
  setComplaint: (complaint: string) => void;
  appointmentType: AppointmentType;
  setAppointmentType: (type: AppointmentType) => void;
  appointmentDate: string;
  setAppointmentDate: (date: string) => void;
  appointmentTime: string;
  setAppointmentTime: (time: string) => void;
  consents: boolean[];
  setConsents: (consents: boolean[]) => void;
  chatHistory: Array<{ role: 'user' | 'assistant'; content: string }>;
  addMessage: (role: 'user' | 'assistant', content: string) => void;
  resetPatientData: () => void;
  clearAllUserData: () => void;
}

const PatientContext = createContext<PatientContextType | undefined>(undefined);

export function PatientProvider({ children }: { children: ReactNode }) {
  const { user, saveUserData, getUserData, clearUserData } = useAuth();

  const [patientName, setPatientName] = useState<string>('');
  const [complaint, setComplaint] = useState<string>('');
  const [appointmentType, setAppointmentType] = useState<AppointmentType>('Initial');
  const [appointmentDate, setAppointmentDate] = useState<string>('');
  const [appointmentTime, setAppointmentTime] = useState<string>('');
  const [consents, setConsents] = useState<boolean[]>([false, false, false]);
  const [chatHistory, setChatHistory] = useState<Array<{ role: 'user' | 'assistant'; content: string }>>([]);
  const [isDataLoaded, setIsDataLoaded] = useState(false);

  // Load user data when user logs in
  useEffect(() => {
    if (user && !isDataLoaded) {
      const userData = getUserData();
      if (userData) {
        // Load existing user data
        setPatientName(userData.patientName || '');
        setComplaint(userData.complaint || '');
        setAppointmentType(userData.appointmentType || 'Initial');
        setAppointmentDate(userData.appointmentDate || '');
        setAppointmentTime(userData.appointmentTime || '');
        setConsents(userData.consents || [false, false, false]);
        // Convert old chat format to new format if needed
        const formattedChatHistory = userData.chatHistory ? userData.chatHistory.map(msg => ({
          role: msg.role,
          content: msg.content
        })) : [];
        setChatHistory(formattedChatHistory);
      } else {
        // Reset to default values for new user
        setPatientName('');
        setComplaint('');
        setAppointmentType('Initial');
        setAppointmentDate('');
        setAppointmentTime('');
        setConsents([false, false, false]);
        setChatHistory([]);
      }
      setIsDataLoaded(true);
    }
  }, [user, getUserData, isDataLoaded]);

  // Save user data whenever any data changes
  useEffect(() => {
    if (user && isDataLoaded) {
      // Get existing data to preserve session count
      const existingData = getUserData();
      const currentSessionCount = existingData ? existingData.totalSessions : 1;

      const dataToSave = {
        patientName,
        complaint,
        appointmentType,
        appointmentDate,
        appointmentTime,
        consents,
        chatHistory: chatHistory.map(msg => ({
          ...msg,
          timestamp: new Date().toISOString()
        })),
        lastLoginDate: new Date().toISOString(),
        totalSessions: currentSessionCount,
      };
      saveUserData(dataToSave);
    }
  }, [user, isDataLoaded, patientName, complaint, appointmentType, appointmentDate, appointmentTime, consents, chatHistory, saveUserData, getUserData]);

  const addMessage = (role: 'user' | 'assistant', content: string) => {
    setChatHistory(prev => [...prev, { role, content }]);
  };

  const resetPatientData = () => {
    setPatientName('');
    setComplaint('');
    setAppointmentType('Initial');
    setAppointmentDate('');
    setAppointmentTime('');
    setConsents([false, false, false]);
    setChatHistory([]);
    setIsDataLoaded(false);
  };

  const clearAllUserData = () => {
    resetPatientData();
    clearUserData();
  };

  // Reset data when user changes or logs out
  useEffect(() => {
    if (!user) {
      resetPatientData();
    } else {
      // Reset isDataLoaded when user changes to ensure fresh data loading
      setIsDataLoaded(false);
    }
  }, [user?.id]); // Watch for user ID changes specifically

  const value = {
    patientName,
    setPatientName,
    complaint,
    setComplaint,
    appointmentType,
    setAppointmentType,
    appointmentDate,
    setAppointmentDate,
    appointmentTime,
    setAppointmentTime,
    consents,
    setConsents,
    chatHistory,
    addMessage,
    resetPatientData,
    clearAllUserData,
  };

  return (
    <PatientContext.Provider value={value}>
      {children}
    </PatientContext.Provider>
  );
}

export function usePatient() {
  const context = useContext(PatientContext);
  if (context === undefined) {
    throw new Error('usePatient must be used within a PatientProvider');
  }
  return context;
}