import React from 'react';

const ThankYouPage: React.FC = () => {
  return (
    <div className="max-w-4xl w-full mx-auto">
      <div className="bg-white rounded-lg overflow-hidden shadow-lg p-12 text-center">
        <div className="mb-8">
          <div className="w-96 h-96 mx-auto flex items-center justify-center">
            <svg
              viewBox="0 0 200 200"
              className="w-full h-full"
            >
              {/* Hexagonal background */}
              <polygon
                points="100,20 160,55 160,125 100,160 40,125 40,55"
                fill="white"
                stroke="#4F46E5"
                strokeWidth="8"
                strokeLinejoin="round"
              />

              {/* Circular checkmark container */}
              <circle
                cx="100"
                cy="100"
                r="25"
                fill="none"
                stroke="#4F46E5"
                strokeWidth="6"
                strokeLinecap="round"
              />

              {/* Checkmark */}
              <path
                d="M85 100 L95 110 L115 85"
                fill="none"
                stroke="#4F46E5"
                strokeWidth="6"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
        <h2 className="text-2xl font-bold mb-4 tracking-wider">THANK YOU FOR TIME!</h2>
        <p className="text-gray-600 text-lg">The doctor will reach out you soon.</p>
      </div>
    </div>
  );
};

export default ThankYouPage;