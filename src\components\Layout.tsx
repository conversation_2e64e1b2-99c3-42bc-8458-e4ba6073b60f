import React, { ReactNode, useState, useEffect, useRef } from 'react';
import { MessageSquare, User, LogOut, ChevronDown } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Complete logout - clears everything and forces fresh login
  const handleLogout = () => {
    console.log('🔴 LOGOUT BUTTON CLICKED - Starting complete logout...');
    console.log('Current user before logout:', user?.username);

    try {
      // Step 1: Clear all user data and session
      logout();
      console.log('✅ User session cleared from AuthContext');

      // Step 2: Force immediate redirect to login page
      navigate('/login', { replace: true });
      console.log('✅ Redirected to login page - user must login fresh');

      // Step 3: Verify localStorage is cleared
      const currentUser = localStorage.getItem('currentUser');
      console.log('localStorage currentUser after logout:', currentUser);

    } catch (error) {
      console.error('❌ Error during logout:', error);
      // Even if there's an error, force redirect to login
      navigate('/login', { replace: true });
    }
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Keyboard shortcut for logout (Ctrl+Shift+L)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'L') {
        event.preventDefault();
        handleLogout();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 relative overflow-hidden">
      {/* Background network effects */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 20 }).map((_, index) => (
          <div
            key={index}
            className="absolute rounded-full bg-white/5"
            style={{
              width: Math.random() * 10 + 2 + 'px',
              height: Math.random() * 10 + 2 + 'px',
              top: Math.random() * 100 + '%',
              left: Math.random() * 100 + '%',
              animation: `pulse ${Math.random() * 4 + 3}s infinite`,
            }}
          />
        ))}

        {/* Network lines */}
        {Array.from({ length: 15 }).map((_, index) => (
          <div
            key={`line-${index}`}
            className="absolute bg-white/10"
            style={{
              height: '1px',
              width: Math.random() * 300 + 100 + 'px',
              top: Math.random() * 100 + '%',
              left: Math.random() * 100 + '%',
              transform: `rotate(${Math.random() * 360}deg)`,
            }}
          />
        ))}
      </div>

      {/* Header with logo and user menu */}
      <header className="absolute top-0 left-0 w-full p-4 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="bg-white/20 p-2 rounded">
              <MessageSquare className="h-5 w-5 text-white" />
            </div>
            <div className="ml-2">
              <h1 className="text-white text-xl font-medium">HealthyStream</h1>
              <p className="text-white/70 text-xs">Accessible healthcare made AI</p>
            </div>
          </div>

          {/* User Menu */}
          {user && (
            <div className="flex items-center space-x-3">
              {/* Logout Button - Always Visible */}
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 bg-red-600/80 hover:bg-red-600 px-3 py-2 rounded-lg transition-colors border border-red-400/30"
                title="Logout"
              >
                <LogOut className="w-4 h-4 text-white" />
                <span className="text-white text-sm font-medium">Logout</span>
              </button>

              {/* User Info Button */}
              <div className="relative" ref={menuRef}>
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-3 bg-purple-600/80 hover:bg-purple-600 px-4 py-2 rounded-lg transition-colors border border-purple-400/30"
                >
                  <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center overflow-hidden border border-white/30">
                    {user.avatar ? (
                      <img src={user.avatar} alt="Avatar" className="w-full h-full object-cover" />
                    ) : (
                      <User className="w-5 h-5 text-white" />
                    )}
                  </div>
                  <span className="text-white text-sm font-medium">{user.username}</span>
                  <ChevronDown className="w-4 h-4 text-white" />
                </button>

                {/* Dropdown Menu */}
                {showUserMenu && (
                  <div
                    className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-xl border border-gray-100 py-2 z-[9999]"
                    style={{
                      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                      border: '2px solid #e5e7eb'
                    }}
                  >
                    {/* User Info Section */}
                    <div className="px-4 py-3 border-b border-gray-100">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center overflow-hidden">
                          {user.avatar ? (
                            <img src={user.avatar} alt="Avatar" className="w-full h-full object-cover" />
                          ) : (
                            <User className="w-5 h-5 text-purple-600" />
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-semibold text-gray-900">{user.username}</p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                        </div>
                      </div>
                    </div>

                    {/* Logout Button */}
                    <div className="px-2 py-1">
                      <button
                        onClick={handleLogout}
                        className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg flex items-center space-x-2 transition-colors group"
                      >
                        <LogOut className="w-4 h-4 group-hover:text-red-700" />
                        <span className="font-medium group-hover:text-red-700">Logout</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        {children}
      </div>


    </div>
  );
};

export default Layout;