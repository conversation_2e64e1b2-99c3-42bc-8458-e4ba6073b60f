# 🔊 Voice Notifications & 📅 Calendar Picker Guide

## 🎯 Overview

Your HealthyStream Chatbot now includes comprehensive voice notifications, appointment alerts, and easy-to-use calendar/time pickers! This feature provides:

1. **🔔 Appointment Time Notifications** - Voice alerts when your appointment is ready
2. **💬 Doctor Response Notifications** - Sound alerts when <PERSON><PERSON> responds
3. **🌐 Browser Notifications** - Alerts that work outside the app
4. **🎵 Pleasant Audio Tones** - Non-intrusive notification sounds
5. **📅 Interactive Calendar Picker** - Easy date selection with visual calendar
6. **🕐 Smart Time Picker** - Quick time selection with preset options

## ✨ New Features Added

### 🏥 Appointment Notifications
- **Voice Announcement**: "Hello [Patient Name], your appointment with Dr<PERSON> is ready. Please proceed to the chat."
- **Two-Tone Chime**: Pleasant musical notification sound
- **Browser Notification**: Shows even when app is in background
- **Countdown Timer**: Real-time display of time remaining until appointment
- **Reminder Alerts**: 5-minute and 1-minute warnings before appointment

### 💬 Doctor Response Notifications
- **Gentle Tone**: Single pleasant notification sound when doctor responds
- **Voice Reading**: Optional text-to-speech of doctor's response
- **Browser Alert**: Shows doctor's message preview
- **Toggle Control**: Voice notifications can be turned on/off in chat

### 🎛️ User Controls
- **Voice Toggle Button**: In chat header (🔊/🔇 icon)
- **Test Notifications**: Button to test the notification system
- **Notification Status**: Shows if permissions are granted
- **Demo Component**: Floating button to test all notification types

### 📅 Calendar & Time Picker Features
- **Visual Calendar**: Click calendar icon to open interactive date picker
- **Month Navigation**: Navigate between months with arrow buttons
- **Date Highlighting**: Today's date highlighted in blue, selected date in indigo
- **Past Date Prevention**: Cannot select dates in the past
- **Quick Time Selection**: Preset appointment times (9 AM, 10 AM, 2 PM, etc.)
- **Custom Time Input**: Hour/minute dropdowns with AM/PM selection
- **Real-time Preview**: See selected time before confirming
- **Smart Formatting**: Automatically formats dates as DD/MM/YYYY

## 🚀 How to Use

### 1. Enable Notifications
When you first visit the app, you'll be prompted to allow notifications. Click "Allow" for the best experience.

### 2. Schedule an Appointment
1. Go to Patient Details page
2. Fill in your information
3. **Select Date**: Click the calendar icon (📅) to open date picker
   - Navigate months with arrow buttons
   - Click on any future date to select
   - Today's date is highlighted in blue
   - Selected date appears in indigo
4. **Select Time**: Click the clock icon (🕐) to open time picker
   - Use "Quick Select" for common appointment times
   - Or use custom dropdowns for specific times
   - Preview shows your selected time
   - Click "Select Time" to confirm
5. Click "Schedule Appointment"
6. You'll see a countdown timer and notification status

### 3. Appointment Ready Alert
When your appointment time arrives:
- 🎵 Two-tone chime plays
- 🗣️ Voice announcement with your name
- 🌐 Browser notification appears
- ✅ "Proceed to Chat" button becomes available

### 4. Chat with Voice Notifications
1. In the chat, you'll see a voice toggle button (🔊/🔇)
2. When enabled, you'll hear:
   - 🎵 Gentle tone when doctor responds
   - 🗣️ Optional voice reading of responses
   - 🌐 Browser notifications for new messages

### 5. Test the System
- Click the floating bell icon (🔔) in bottom-right corner
- Use the demo panel to test different notification types
- Check system status and permissions

## 🔧 Technical Features

### Audio System
- **Web Audio API**: Generates pleasant notification tones
- **Frequency Tuning**: 880Hz/660Hz chime, 750Hz response tone
- **Fade Envelopes**: Smooth audio transitions
- **Cross-browser Support**: Works on modern browsers

### Speech Synthesis
- **Text-to-Speech**: Reads appointment and response notifications
- **Voice Selection**: Automatically chooses pleasant female voices
- **Rate Control**: Optimized speaking speed (0.9x rate)
- **Volume Control**: Set to 80% for comfortable listening

### Browser Notifications
- **Permission Handling**: Automatic permission requests
- **Rich Notifications**: Include icons, actions, and previews
- **Background Alerts**: Work when app is not focused
- **Action Buttons**: Quick actions from notifications

### Real-time Monitoring
- **Appointment Timer**: Updates every second
- **Time Formatting**: Shows days, hours, minutes, seconds
- **Status Tracking**: Monitors appointment readiness
- **Automatic Triggers**: No manual intervention needed

## 🎨 User Interface

### Visual Indicators
- **🔊 Voice On**: Green background, Volume2 icon
- **🔇 Voice Off**: Gray background, VolumeX icon
- **📅 Scheduled**: Blue notification with countdown
- **🔔 Ready**: Green notification with proceed button
- **⚠️ Permissions**: Warning if notifications disabled

### Notification Styles
- **Appointment Ready**: Green theme with checkmark
- **Doctor Response**: Blue theme with message preview
- **System Test**: Purple theme with test confirmation
- **Status Updates**: Gray theme with system info

## 🛠️ Files Modified/Added

### New Files
- `src/services/notificationService.ts` - Core notification system
- `src/hooks/useAppointmentNotifications.ts` - Appointment monitoring hook
- `src/components/NotificationDemo.tsx` - Testing interface
- `src/components/DatePicker.tsx` - Interactive calendar component
- `src/components/TimePicker.tsx` - Smart time selection component

### Modified Files
- `src/services/openai.ts` - Added response callback
- `src/pages/ChatPage.tsx` - Added voice toggle and notifications
- `src/pages/PatientDetailsPage.tsx` - Integrated appointment notifications
- `src/App.tsx` - Added demo component

## 🔍 Browser Compatibility

### Supported Features
- **Chrome/Edge**: Full support (Audio, Speech, Notifications)
- **Firefox**: Full support (Audio, Speech, Notifications)
- **Safari**: Partial support (Audio, limited Speech)
- **Mobile**: Varies by browser and OS

### Fallback Behavior
- If audio not supported: Visual notifications only
- If speech not supported: Audio tones only
- If notifications blocked: In-app alerts only

## 🎯 Usage Tips

### For Best Experience
1. **Allow Notifications**: Click "Allow" when prompted
2. **Keep Tab Open**: For real-time appointment monitoring
3. **Test System**: Use demo to verify everything works
4. **Adjust Volume**: Set comfortable system volume
5. **Enable Voice**: Toggle on for full experience

### Troubleshooting
- **No Sound**: Check browser audio permissions
- **No Notifications**: Check browser notification settings
- **No Voice**: Check if speech synthesis is enabled
- **Timer Issues**: Refresh page if countdown stops

## 🔮 Future Enhancements

Potential improvements for future versions:
- **Custom Ringtones**: Upload personal notification sounds
- **Snooze Options**: Delay appointment notifications
- **Multiple Languages**: Voice announcements in different languages
- **Smart Scheduling**: AI-powered appointment suggestions
- **Integration**: Calendar sync and external notifications

## 📞 Support

If you experience any issues with the notification system:
1. Use the test buttons to diagnose problems
2. Check browser console for error messages
3. Verify notification permissions in browser settings
4. Try refreshing the page to reset the system

---

**Enjoy your enhanced HealthyStream experience with voice notifications! 🎉**
