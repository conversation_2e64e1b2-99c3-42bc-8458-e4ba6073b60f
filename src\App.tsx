import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { PatientProvider } from './contexts/PatientContext';
import { AuthProvider } from './contexts/AuthContext';

// Pages
import WelcomePage from './pages/WelcomePage';
import PatientDetailsPage from './pages/PatientDetailsPage';
import ChatPage from './pages/ChatPage';
import ThankYouPage from './pages/ThankYouPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import NotificationDemo from './components/NotificationDemo';

function App() {
  return (
    <AuthProvider>
      <PatientProvider>
        <Router>
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />

            {/* Default route redirects to login */}
            <Route path="/" element={<Navigate to="/login" replace />} />

            {/* Protected Routes */}
            <Route path="/home" element={
              <ProtectedRoute>
                <Layout>
                  <WelcomePage />
                </Layout>
              </ProtectedRoute>
            } />
            <Route path="/patient-details" element={
              <ProtectedRoute>
                <Layout>
                  <PatientDetailsPage />
                </Layout>
              </ProtectedRoute>
            } />
            <Route path="/chat" element={
              <ProtectedRoute>
                <Layout>
                  <ChatPage />
                </Layout>
              </ProtectedRoute>
            } />
            <Route path="/thank-you" element={
              <ProtectedRoute>
                <Layout>
                  <ThankYouPage />
                </Layout>
              </ProtectedRoute>
            } />
          </Routes>
          {/* Notification Demo - Available on all protected routes */}
          <NotificationDemo />
        </Router>
      </PatientProvider>
    </AuthProvider>
  );
}

export default App