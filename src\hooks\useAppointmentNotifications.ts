import { useEffect, useRef, useState } from 'react';
import { notificationService } from '../services/notificationService';

interface UseAppointmentNotificationsProps {
  appointmentDate: string;
  appointmentTime: string;
  patientName?: string;
  isAppointmentScheduled: boolean;
  onAppointmentReady?: () => void;
}

export const useAppointmentNotifications = ({
  appointmentDate,
  appointmentTime,
  patientName,
  isAppointmentScheduled,
  onAppointmentReady
}: UseAppointmentNotificationsProps) => {
  const [isAppointmentTimeReached, setIsAppointmentTimeReached] = useState(false);
  const [timeUntilAppointment, setTimeUntilAppointment] = useState<string>('');
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const notificationSentRef = useRef(false);
  const oneMinuteWarningSentRef = useRef(false);
  const fiveMinuteWarningSentRef = useRef(false);

  // Parse appointment date and time
  const parseAppointmentDateTime = (date: string, time: string): Date | null => {
    if (!date || !time) return null;

    try {
      // Handle different date formats
      let parsedDate: Date;

      if (date.includes('/')) {
        // DD/MM/YYYY or MM/DD/YYYY format
        const [part1, part2, year] = date.split('/');
        // Assume DD/MM/YYYY format (European style)
        parsedDate = new Date(parseInt(year), parseInt(part2) - 1, parseInt(part1));
      } else if (date.includes('-')) {
        // YYYY-MM-DD format
        parsedDate = new Date(date);
      } else {
        return null;
      }

      // Handle time format (HH:MM or HH:MM AM/PM)
      let hours: number, minutes: number;

      if (time.toLowerCase().includes('am') || time.toLowerCase().includes('pm')) {
        // 12-hour format
        const [timePart, period] = time.split(/\s+/);
        const [hourStr, minuteStr] = timePart.split(':');
        hours = parseInt(hourStr);
        minutes = parseInt(minuteStr);

        if (period.toLowerCase() === 'pm' && hours !== 12) {
          hours += 12;
        } else if (period.toLowerCase() === 'am' && hours === 12) {
          hours = 0;
        }
      } else {
        // 24-hour format
        const [hourStr, minuteStr] = time.split(':');
        hours = parseInt(hourStr);
        minutes = parseInt(minuteStr);
      }

      parsedDate.setHours(hours, minutes, 0, 0);
      return parsedDate;
    } catch (error) {
      console.error('Error parsing appointment date/time:', error);
      return null;
    }
  };

  // Format time remaining until appointment
  const formatTimeRemaining = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const days = Math.floor(totalSeconds / (24 * 3600));
    const hours = Math.floor((totalSeconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Check appointment status
  const checkAppointmentStatus = () => {
    if (!isAppointmentScheduled || !appointmentDate || !appointmentTime) {
      return;
    }

    const appointmentDateTime = parseAppointmentDateTime(appointmentDate, appointmentTime);
    if (!appointmentDateTime) return;

    const now = new Date();
    const timeDifference = appointmentDateTime.getTime() - now.getTime();

    if (timeDifference <= 0) {
      // Appointment time has arrived or passed
      if (!isAppointmentTimeReached) {
        setIsAppointmentTimeReached(true);
        setTimeUntilAppointment('Ready now!');

        // Send notification only once
        if (!notificationSentRef.current) {
          notificationService.announceAppointmentReady(patientName);
          notificationSentRef.current = true;

          // Call the callback if provided
          if (onAppointmentReady) {
            onAppointmentReady();
          }
        }
      }
    } else {
      // Appointment is in the future
      setIsAppointmentTimeReached(false);
      setTimeUntilAppointment(formatTimeRemaining(timeDifference));
      notificationSentRef.current = false;

      // Send reminder notifications at specific intervals
      const minutesUntil = Math.floor(timeDifference / (1000 * 60));

      // Notify 5 minutes before appointment
      if (minutesUntil === 5 && !fiveMinuteWarningSentRef.current) {
        notificationService.speakNotification(
          `Reminder: Your appointment with Dr. Michael is in 5 minutes.`,
          0.9,
          1.1
        );
        fiveMinuteWarningSentRef.current = true;
      }

      // Notify 1 minute before appointment with enhanced warning
      if (minutesUntil === 1 && !oneMinuteWarningSentRef.current) {
        notificationService.announceOneMinuteWarning(patientName);
        oneMinuteWarningSentRef.current = true;
      }
    }
  };

  // Start monitoring appointment
  useEffect(() => {
    if (isAppointmentScheduled && appointmentDate && appointmentTime) {
      // Check immediately
      checkAppointmentStatus();

      // Set up interval to check every second
      intervalRef.current = setInterval(checkAppointmentStatus, 1000);
    } else {
      // Clear interval if appointment is not scheduled
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsAppointmentTimeReached(false);
      setTimeUntilAppointment('');
      notificationSentRef.current = false;
      oneMinuteWarningSentRef.current = false;
      fiveMinuteWarningSentRef.current = false;
    }

    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isAppointmentScheduled, appointmentDate, appointmentTime, patientName]);

  // Test notification function
  const testNotification = () => {
    notificationService.testNotifications();
  };

  return {
    isAppointmentTimeReached,
    timeUntilAppointment,
    testNotification,
    notificationStatus: notificationService.getNotificationStatus()
  };
};
