import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Send, ArrowLeft, User, Volume2, VolumeX } from 'lucide-react';
import { usePatient } from '../contexts/PatientContext';
import { useAuth } from '../contexts/AuthContext';
import { getChatResponse } from '../services/openai';
import { notificationService } from '../services/notificationService';
import doctorAvatar from '../assets/doctor-avatar.png';

const ChatPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    patientName,
    chatHistory,
    addMessage
  } = usePatient();
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [voiceNotificationsEnabled, setVoiceNotificationsEnabled] = useState(true);
  const [isVoiceReading, setIsVoiceReading] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Initialize chat with welcome message only for truly new users
  useEffect(() => {
    // Only add initial message if chat is completely empty and this is a new session
    if (chatHistory.length === 0) {
      const welcomeMessage = {
        role: 'assistant' as const,
        content: `Hello ${patientName || 'there'}! I'm Dr. Michael, your digital healthcare assistant. I'm here to help you with your health concerns. Please tell me what brings you here today?`
      };

      addMessage(welcomeMessage.role, welcomeMessage.content);
    }
  }, [addMessage, chatHistory.length, patientName]);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatHistory]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!inputMessage.trim()) return;

    addMessage('user', inputMessage);
    setInputMessage('');
    setIsTyping(true);

    try {
      const messages = chatHistory.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      messages.push({
        role: 'user' as const,
        content: inputMessage
      });

      const response = await getChatResponse(messages);

      if (response) {
        addMessage('assistant', response);

        // Trigger notification when doctor responds (after message is added)
        setTimeout(() => {
          if (voiceNotificationsEnabled) {
            setIsVoiceReading(true);

            // Monitor voice reading status
            const checkVoiceStatus = () => {
              if (!notificationService.isVoiceSpeaking()) {
                setIsVoiceReading(false);
              } else {
                setTimeout(checkVoiceStatus, 500);
              }
            };

            // Start monitoring after a short delay
            setTimeout(checkVoiceStatus, 1000);
          }

          notificationService.announceDoctorResponse(response, voiceNotificationsEnabled);
        }, 100);
      }
    } catch (error) {
      console.error('Error getting chat response:', error);
      addMessage('assistant', "I'm sorry, I'm having trouble connecting right now. Please try again.");
    } finally {
      setIsTyping(false);
    }
  };

  const handleEndChat = () => {
    // Stop any ongoing voice reading when ending chat
    notificationService.stopVoiceReading();
    navigate('/thank-you');
  };

  const handleVoiceToggle = () => {
    const newVoiceState = !voiceNotificationsEnabled;
    setVoiceNotificationsEnabled(newVoiceState);

    // If turning off voice, stop any current reading
    if (!newVoiceState) {
      notificationService.stopVoiceReading();
      setIsVoiceReading(false);
    }
  };

  const doctorImageUrl = doctorAvatar;
  // Use user's uploaded avatar if available, otherwise use default
  const patientImageUrl = user?.avatar || "https://img.freepik.com/free-photo/3d-illustration-person-with-sunglasses_23-**********.jpg?semt=ais_hybrid";

  return (
    <div className="max-w-4xl w-full mx-auto h-screen flex flex-col">
      <div className="bg-white shadow-lg flex flex-col h-[90vh]">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
          <div className="flex items-center">
            <button
              onClick={() => navigate('/patient-details')}
              className="mr-3 p-1 hover:bg-gray-100 rounded"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <div className="flex items-center">
              <img
                src={doctorImageUrl}
                alt="Doctor Avatar"
                className="w-10 h-10 rounded-full mr-3 object-cover"
              />
              <div>
                <h2 className="text-lg font-semibold text-gray-800">Dr. Michael</h2>
                <p className="text-gray-500 text-sm">Digital Doctor</p>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {/* Voice Reading Status */}
            {isVoiceReading && (
              <div className="flex items-center space-x-2 bg-blue-100 px-3 py-1 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-blue-700 font-medium">Reading...</span>
                <button
                  onClick={() => {
                    notificationService.stopVoiceReading();
                    setIsVoiceReading(false);
                  }}
                  className="text-blue-600 hover:text-blue-800 text-xs"
                  title="Stop reading"
                >
                  Stop
                </button>
              </div>
            )}

            {/* Voice Toggle Button */}
            <button
              onClick={handleVoiceToggle}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                voiceNotificationsEnabled
                  ? 'bg-green-100 text-green-600 hover:bg-green-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              title={voiceNotificationsEnabled ? 'Voice reading ON - Doctor messages will be read aloud' : 'Voice reading OFF - Text only'}
            >
              {voiceNotificationsEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
            </button>
            <div className="text-right">
              <p className="text-sm text-gray-600">Patient: {patientName}</p>
            </div>
            {user?.avatar ? (
              <img
                src={user.avatar}
                alt="Patient Avatar"
                className="w-8 h-8 rounded-full object-cover border border-gray-200"
              />
            ) : (
              <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center border border-gray-200">
                <User className="w-4 h-4 text-blue-600" />
              </div>
            )}
          </div>
        </div>

        {/* Chat Messages */}
        <div
          ref={chatContainerRef}
          className="flex-1 p-4 overflow-y-auto space-y-4 bg-gray-50"
          style={{ minHeight: '500px', maxHeight: '600px' }}
        >
          {chatHistory.map((message, index) => (
            <div
              key={index}
              className={`flex items-start space-x-3 ${message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}
            >
              {/* Avatar */}
              <div className="flex-shrink-0">
                {message.role === 'user' ? (
                  user?.avatar ? (
                    <img
                      src={user.avatar}
                      alt="Patient"
                      className="w-8 h-8 rounded-full object-cover border border-gray-200"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center border border-gray-200">
                      <User className="w-4 h-4 text-blue-600" />
                    </div>
                  )
                ) : (
                  <img
                    src={doctorImageUrl}
                    alt="Doctor"
                    className="w-8 h-8 rounded-full object-cover border border-gray-200"
                  />
                )}
              </div>

              {/* Message */}
              <div
                className={`max-w-xs lg:max-w-md xl:max-w-lg px-4 py-3 rounded-2xl text-sm ${
                  message.role === 'user'
                    ? 'bg-blue-500 text-white rounded-br-md'
                    : 'bg-white text-gray-800 border border-gray-200 rounded-bl-md'
                }`}
              >
                <p className="leading-relaxed">{message.content}</p>
                {message.role === 'assistant' && voiceNotificationsEnabled && (
                  <div className="flex items-center mt-2 pt-2 border-t border-gray-100">
                    <Volume2 className="w-3 h-3 mr-1 text-green-600" />
                    <span className="text-xs text-green-600 font-medium">Voice reading enabled</span>
                  </div>
                )}
                {message.role === 'assistant' && !voiceNotificationsEnabled && (
                  <div className="flex items-center mt-2 pt-2 border-t border-gray-100">
                    <VolumeX className="w-3 h-3 mr-1 text-gray-400" />
                    <span className="text-xs text-gray-400">Text only</span>
                  </div>
                )}
              </div>
            </div>
          ))}

          {isTyping && (
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <img
                  src={doctorImageUrl}
                  alt="Doctor"
                  className="w-8 h-8 rounded-full object-cover"
                />
              </div>
              <div className="bg-white text-gray-800 border border-gray-200 px-4 py-3 rounded-2xl rounded-bl-md">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Message Input */}
        <div className="p-4 border-t border-gray-200 bg-white">
          <form onSubmit={handleSendMessage} className="flex space-x-2 mb-3">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="Type your message..."
              className="flex-1 border border-gray-300 rounded-full px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isTyping}
            />
            <button
              type="submit"
              disabled={isTyping || !inputMessage.trim()}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-6 py-3 rounded-full transition-colors duration-200 flex items-center justify-center"
            >
              <Send className="h-4 w-4" />
            </button>
          </form>

          {/* End Chat Button */}
          <div className="flex justify-end">
            <button
              onClick={handleEndChat}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-full text-sm transition-colors duration-200"
            >
              End Chat
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatPage;