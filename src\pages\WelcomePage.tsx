import React from 'react';
import { useNavigate } from 'react-router-dom';
import doctorAvatar from '../assets/doctor-avatar.png';
import WelcomeBackMessage from '../components/WelcomeBackMessage';
const WelcomePage: React.FC = () => {
  const navigate = useNavigate();

  const handleStartChat = () => {
    navigate('/patient-details');
  };

  return (
    <>
      <WelcomeBackMessage />
      <div className="max-w-md w-full mx-auto">
        <div className="bg-white rounded-lg overflow-hidden shadow-lg animate-fadeIn">
        {/* Image Section */}
        <div className="p-6 pb-0 flex flex-col items-center">
          <div className="w-full h-60 mx-auto overflow-hidden rounded-lg flex items-center justify-center">
            <img
              src={doctorAvatar}
              alt="Doctor Avatar"
              className="h-full w-auto object-contain"
            />
          </div>
        </div>

        {/* Blue Section - touching the image */}
        <div className="w-full py-8 px-6 text-center bg-indigo-900">
          <h2 className="text-3xl font-bold text-white mb-2">Hello!</h2>
          <p className="text-xl font-medium text-white mb-1">I'm Doctor Michael</p>
          <p className="text-white/80 mb-1">Your digital doctor.</p>
          <p className="text-white/80 text-sm max-w-xs mx-auto mb-6">
            I will help you to diagnose your problem by ask step by step questions in chat.
          </p>

          {/* Button on blue background */}
          <button
            onClick={handleStartChat}
            className="bg-white hover:bg-indigo-100 text-indigo-900 py-3 px-8 rounded-md font-medium transition-colors duration-300 flex items-center justify-center mx-auto"
          >
            Let's Start Chat
          </button>
        </div>
        </div>
      </div>
    </>
  );
}

export default WelcomePage;