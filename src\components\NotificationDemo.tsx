import React, { useState } from 'react';
import { Bell, Volume2, Play, Settings } from 'lucide-react';
import { notificationService } from '../services/notificationService';

const NotificationDemo: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [notificationStatus, setNotificationStatus] = useState(
    notificationService.getNotificationStatus()
  );

  const handleTestAppointmentNotification = () => {
    notificationService.announceAppointmentReady('<PERSON>');
  };

  const handleTestDoctorResponse = () => {
    const sampleResponse = "Thank you for sharing your symptoms. Based on what you've described, it sounds like you might be experiencing a common cold. I recommend getting plenty of rest, staying hydrated, and taking over-the-counter pain relievers if needed. If your symptoms worsen or persist for more than a week, please consult with a healthcare professional for further evaluation.";
    notificationService.announceDoctorResponse(sampleResponse, true);
  };

  const handleTestVoiceOnly = () => {
    const longMessage = "Good morning! I hope you're feeling better today. Based on our previous conversation and the symptoms you described, I want to provide you with some comprehensive advice. First, it's important to get adequate rest - aim for 7 to 8 hours of sleep each night. Second, maintain proper hydration by drinking plenty of water throughout the day. Third, consider taking vitamin C supplements to boost your immune system. If you experience any worsening symptoms such as high fever, difficulty breathing, or severe headaches, please seek immediate medical attention. Remember, I'm here to help guide you, but always consult with a licensed healthcare provider for serious medical concerns.";
    notificationService.speakDoctorMessage(longMessage);
  };

  const handleTestOneMinuteWarning = () => {
    notificationService.announceOneMinuteWarning('John Doe');
  };

  const handleTestSimpleNotification = () => {
    notificationService.testNotifications();
  };

  const refreshStatus = () => {
    setNotificationStatus(notificationService.getNotificationStatus());
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg transition-colors duration-200 z-50"
        title="Test Notifications"
      >
        <Bell className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-80 z-50">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
          <Bell className="w-5 h-5 mr-2" />
          Notification Demo
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600 text-xl"
        >
          ×
        </button>
      </div>

      {/* Notification Status */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-medium text-gray-700">System Status</h4>
          <button
            onClick={refreshStatus}
            className="text-blue-500 hover:text-blue-600 text-sm"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span>Browser Notifications:</span>
            <span className={notificationStatus.permission ? 'text-green-600' : 'text-red-600'}>
              {notificationStatus.permission ? '✅ Enabled' : '❌ Disabled'}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Audio Support:</span>
            <span className={notificationStatus.audioSupport ? 'text-green-600' : 'text-red-600'}>
              {notificationStatus.audioSupport ? '✅ Available' : '❌ Not Available'}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Speech Support:</span>
            <span className={notificationStatus.speechSupport ? 'text-green-600' : 'text-red-600'}>
              {notificationStatus.speechSupport ? '✅ Available' : '❌ Not Available'}
            </span>
          </div>
        </div>
      </div>

      {/* Test Buttons */}
      <div className="space-y-3">
        <button
          onClick={handleTestAppointmentNotification}
          className="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
        >
          <Bell className="w-4 h-4" />
          <span>Test Appointment Ready</span>
        </button>

        <button
          onClick={handleTestOneMinuteWarning}
          className="w-full bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
        >
          <Bell className="w-4 h-4" />
          <span>Test 1-Minute Warning</span>
        </button>

        <button
          onClick={handleTestDoctorResponse}
          className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
        >
          <Volume2 className="w-4 h-4" />
          <span>Test Doctor Response</span>
        </button>

        <button
          onClick={handleTestVoiceOnly}
          className="w-full bg-indigo-500 hover:bg-indigo-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
        >
          <Volume2 className="w-4 h-4" />
          <span>Test Long Voice Reading</span>
        </button>

        <button
          onClick={handleTestSimpleNotification}
          className="w-full bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
        >
          <Play className="w-4 h-4" />
          <span>Test Simple Notification</span>
        </button>
      </div>

      {/* Instructions */}
      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h4 className="text-sm font-medium text-yellow-800 mb-1">How it works:</h4>
        <ul className="text-xs text-yellow-700 space-y-1">
          <li>• <strong>Appointment Ready:</strong> Chime + voice + outside-app notification</li>
          <li>• <strong>1-Minute Warning:</strong> Voice: "starts in one minute. Please be ready"</li>
          <li>• <strong>Doctor Response:</strong> Double tone + complete message reading</li>
          <li>• <strong>Long Voice Reading:</strong> Tests complete message reading</li>
          <li>• <strong>Voice Toggle:</strong> ON = text + voice, OFF = text only</li>
          <li>• <strong>Stop Reading:</strong> Click "Stop" button when voice is active</li>
        </ul>
      </div>

      {!notificationStatus.permission && (
        <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
          <strong>Note:</strong> Please allow notifications when prompted for the best experience.
        </div>
      )}
    </div>
  );
};

export default NotificationDemo;
