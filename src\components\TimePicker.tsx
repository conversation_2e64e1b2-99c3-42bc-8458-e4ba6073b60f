import React, { useState, useRef, useEffect } from 'react';
import { Clock, X } from 'lucide-react';

interface TimePickerProps {
  value: string;
  onChange: (time: string) => void;
  disabled?: boolean;
  placeholder?: string;
  error?: boolean;
  format24?: boolean; // true for 24-hour, false for 12-hour
}

const TimePicker: React.FC<TimePickerProps> = ({
  value,
  onChange,
  disabled = false,
  placeholder = "Select time",
  error = false,
  format24 = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedHour, setSelectedHour] = useState(9); // Default to 9 AM
  const [selectedMinute, setSelectedMinute] = useState(0);
  const [selectedPeriod, setSelectedPeriod] = useState<'AM' | 'PM'>('AM');
  const containerRef = useRef<HTMLDivElement>(null);

  // Close time picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Parse current value when component mounts or value changes
  useEffect(() => {
    if (value) {
      parseTimeValue(value);
    }
  }, [value]);

  const parseTimeValue = (timeStr: string) => {
    if (!timeStr) return;

    try {
      if (timeStr.toLowerCase().includes('am') || timeStr.toLowerCase().includes('pm')) {
        // 12-hour format
        const isPM = timeStr.toLowerCase().includes('pm');
        const timeOnly = timeStr.replace(/am|pm/gi, '').trim();
        const [hourStr, minuteStr] = timeOnly.split(':');
        const hour = parseInt(hourStr);
        const minute = parseInt(minuteStr) || 0;
        
        setSelectedHour(hour);
        setSelectedMinute(minute);
        setSelectedPeriod(isPM ? 'PM' : 'AM');
      } else {
        // 24-hour format
        const [hourStr, minuteStr] = timeStr.split(':');
        const hour = parseInt(hourStr);
        const minute = parseInt(minuteStr) || 0;
        
        if (format24) {
          setSelectedHour(hour);
        } else {
          // Convert to 12-hour format
          if (hour === 0) {
            setSelectedHour(12);
            setSelectedPeriod('AM');
          } else if (hour <= 12) {
            setSelectedHour(hour);
            setSelectedPeriod(hour === 12 ? 'PM' : 'AM');
          } else {
            setSelectedHour(hour - 12);
            setSelectedPeriod('PM');
          }
        }
        setSelectedMinute(minute);
      }
    } catch (error) {
      console.error('Error parsing time:', error);
    }
  };

  const formatTime = (): string => {
    if (format24) {
      return `${selectedHour.toString().padStart(2, '0')}:${selectedMinute.toString().padStart(2, '0')}`;
    } else {
      return `${selectedHour}:${selectedMinute.toString().padStart(2, '0')} ${selectedPeriod}`;
    }
  };

  const handleTimeSelect = () => {
    const formattedTime = formatTime();
    onChange(formattedTime);
    setIsOpen(false);
  };

  const generateHours = (): number[] => {
    if (format24) {
      return Array.from({ length: 24 }, (_, i) => i);
    } else {
      return Array.from({ length: 12 }, (_, i) => i + 1);
    }
  };

  const generateMinutes = (): number[] => {
    return Array.from({ length: 12 }, (_, i) => i * 5); // 0, 5, 10, 15, ..., 55
  };

  const getQuickTimes = (): string[] => {
    if (format24) {
      return ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'];
    } else {
      return ['9:00 AM', '10:00 AM', '11:00 AM', '2:00 PM', '3:00 PM', '4:00 PM'];
    }
  };

  const handleQuickTimeSelect = (time: string) => {
    onChange(time);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={containerRef}>
      {/* Input Field */}
      <div className="flex items-center">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className={`bg-indigo-100 p-2 rounded-md transition-colors duration-200 ${
            disabled 
              ? 'cursor-not-allowed opacity-50' 
              : 'hover:bg-indigo-200 cursor-pointer'
          }`}
        >
          <Clock size={20} className="text-indigo-700" />
        </button>
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          placeholder={placeholder}
          className={`ml-2 p-2 border ${
            error ? 'border-red-500' : 'border-gray-300'
          } rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 w-full ${
            disabled ? 'bg-gray-100 cursor-not-allowed' : ''
          }`}
          onClick={() => !disabled && setIsOpen(true)}
          readOnly
        />
      </div>

      {/* Time Picker Popup */}
      {isOpen && !disabled && (
        <div className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-4 min-w-[320px]">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">Select Time</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X size={20} />
            </button>
          </div>

          {/* Quick Time Selection */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Quick Select</h4>
            <div className="grid grid-cols-3 gap-2">
              {getQuickTimes().map(time => (
                <button
                  key={time}
                  onClick={() => handleQuickTimeSelect(time)}
                  className="px-3 py-2 text-sm bg-gray-100 hover:bg-indigo-100 text-gray-700 hover:text-indigo-700 rounded transition-colors duration-200"
                >
                  {time}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Time Selection */}
          <div className="border-t border-gray-200 pt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Custom Time</h4>
            
            <div className="flex items-center space-x-2 mb-4">
              {/* Hour Selection */}
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1">Hour</label>
                <select
                  value={selectedHour}
                  onChange={(e) => setSelectedHour(parseInt(e.target.value))}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  {generateHours().map(hour => (
                    <option key={hour} value={hour}>
                      {format24 ? hour.toString().padStart(2, '0') : hour}
                    </option>
                  ))}
                </select>
              </div>

              <div className="text-gray-500 text-xl font-bold">:</div>

              {/* Minute Selection */}
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1">Minute</label>
                <select
                  value={selectedMinute}
                  onChange={(e) => setSelectedMinute(parseInt(e.target.value))}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  {generateMinutes().map(minute => (
                    <option key={minute} value={minute}>
                      {minute.toString().padStart(2, '0')}
                    </option>
                  ))}
                </select>
              </div>

              {/* AM/PM Selection */}
              {!format24 && (
                <div className="flex-1">
                  <label className="block text-xs text-gray-600 mb-1">Period</label>
                  <select
                    value={selectedPeriod}
                    onChange={(e) => setSelectedPeriod(e.target.value as 'AM' | 'PM')}
                    className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="AM">AM</option>
                    <option value="PM">PM</option>
                  </select>
                </div>
              )}
            </div>

            {/* Preview */}
            <div className="bg-gray-50 p-3 rounded mb-4">
              <div className="text-sm text-gray-600">Selected time:</div>
              <div className="text-lg font-semibold text-gray-800">{formatTime()}</div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-2">
              <button
                onClick={handleTimeSelect}
                className="flex-1 bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700 transition-colors duration-200"
              >
                Select Time
              </button>
              <button
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimePicker;
