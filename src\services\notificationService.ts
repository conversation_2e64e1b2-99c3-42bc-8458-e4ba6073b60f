// Notification Service for Voice Alerts and Browser Notifications

export class NotificationService {
  private static instance: NotificationService;
  private audioContext: AudioContext | null = null;
  private isNotificationPermissionGranted = false;

  private constructor() {
    this.initializeAudioContext();
    this.requestNotificationPermission();
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  private initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Audio context not supported:', error);
    }
  }

  private async requestNotificationPermission() {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      this.isNotificationPermissionGranted = permission === 'granted';
    }
  }

  // Generate a pleasant notification tone
  private generateNotificationTone(frequency: number = 800, duration: number = 0.3) {
    if (!this.audioContext) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
    oscillator.type = 'sine';

    // Create a pleasant fade in/out envelope
    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.3, this.audioContext.currentTime + 0.05);
    gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + duration - 0.05);
    gainNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + duration);

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + duration);
  }

  // Generate a two-tone chime for appointment notifications
  private generateAppointmentChime() {
    if (!this.audioContext) return;

    // First tone (higher)
    setTimeout(() => this.generateNotificationTone(880, 0.4), 0);
    // Second tone (lower)
    setTimeout(() => this.generateNotificationTone(660, 0.6), 200);
  }

  // Generate a single gentle tone for doctor responses
  private generateResponseTone() {
    this.generateNotificationTone(750, 0.4);
  }

  // Play appointment ready notification with automatic outside-app alert
  public playAppointmentReadyNotification() {
    this.generateAppointmentChime();

    // Always show browser notification for appointment ready (outside app)
    if (this.isNotificationPermissionGranted) {
      const notification = new Notification('🏥 HealthyStream - Appointment Ready', {
        body: 'Your appointment time has arrived! Dr. Michael is ready to see you.',
        icon: '/doctor-avatar.png',
        badge: '/doctor-avatar.png',
        tag: 'appointment-ready',
        requireInteraction: true,
        silent: false, // Ensure sound plays
        actions: [
          {
            action: 'start-chat',
            title: 'Start Chat with Doctor'
          }
        ]
      });

      // Handle notification click to focus the app
      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      // Auto-close after 30 seconds if not interacted with
      setTimeout(() => {
        notification.close();
      }, 30000);
    }
  }

  // Play doctor response notification with enhanced sound
  public playDoctorResponseNotification(message?: string) {
    // Play a more noticeable sound for doctor responses
    this.generateResponseTone();

    // Add a second gentle tone for emphasis
    setTimeout(() => {
      this.generateNotificationTone(600, 0.2);
    }, 300);

    if (this.isNotificationPermissionGranted) {
      const truncatedMessage = message && message.length > 100
        ? message.substring(0, 100) + '...'
        : message || 'Dr. Michael has responded to your message.';

      const notification = new Notification('💬 Dr. Michael Responded', {
        body: truncatedMessage,
        icon: '/doctor-avatar.png',
        badge: '/doctor-avatar.png',
        tag: 'doctor-response',
        silent: false,
        requireInteraction: false
      });

      // Handle notification click to focus the app
      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      // Auto-close after 10 seconds
      setTimeout(() => {
        notification.close();
      }, 10000);
    }
  }

  // Text-to-speech for important notifications
  public speakNotification(text: string, rate: number = 1.0, pitch: number = 1.0) {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = rate;
      utterance.pitch = pitch;
      utterance.volume = 0.8;

      // Try to use a pleasant voice
      const voices = speechSynthesis.getVoices();
      const preferredVoice = voices.find(voice =>
        voice.name.includes('Female') ||
        voice.name.includes('Samantha') ||
        voice.name.includes('Karen')
      );

      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      speechSynthesis.speak(utterance);
    }
  }

  // One minute warning with special notification
  public announceOneMinuteWarning(patientName?: string) {
    // Play a gentle warning tone
    this.generateNotificationTone(700, 0.5);

    const message = patientName
      ? `${patientName}, your appointment with Dr. Michael starts in one minute. Please be ready.`
      : 'Your appointment with Dr. Michael starts in one minute. Please be ready.';

    // Voice announcement
    setTimeout(() => {
      this.speakNotification(message, 0.9, 1.1);
    }, 600);

    // Optional browser notification for 1-minute warning
    if (this.isNotificationPermissionGranted) {
      const notification = new Notification('⏰ 1 Minute Warning', {
        body: 'Your appointment with Dr. Michael starts in one minute. Please be ready.',
        icon: '/doctor-avatar.png',
        badge: '/doctor-avatar.png',
        tag: 'one-minute-warning',
        silent: false,
        requireInteraction: false
      });

      // Auto-close after 8 seconds
      setTimeout(() => {
        notification.close();
      }, 8000);
    }
  }

  // Appointment ready with voice announcement
  public announceAppointmentReady(patientName?: string) {
    this.playAppointmentReadyNotification();

    const message = patientName
      ? `Hello ${patientName}, your appointment with Dr. Michael is ready. Please proceed to the chat.`
      : 'Your appointment with Dr. Michael is ready. Please proceed to the chat.';

    // Delay speech slightly to avoid overlap with notification sound
    setTimeout(() => {
      this.speakNotification(message, 0.9, 1.1);
    }, 1000);
  }

  // Doctor response with complete voice reading
  public announceDoctorResponse(message?: string, enableVoice: boolean = false) {
    // Always play the notification sound
    this.playDoctorResponseNotification(message);

    // If voice is enabled, read the complete message
    if (enableVoice && message) {
      // Delay speech to avoid overlap with notification sound
      setTimeout(() => {
        this.speakDoctorMessage(message);
      }, 800);
    }
  }

  // Dedicated method for reading doctor messages with enhanced voice
  public speakDoctorMessage(message: string) {
    if ('speechSynthesis' in window && message) {
      // Cancel any ongoing speech
      speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(message);

      // Enhanced voice settings for doctor messages
      utterance.rate = 0.85; // Slightly slower for medical content
      utterance.pitch = 1.0; // Natural pitch
      utterance.volume = 0.9; // Clear volume

      // Try to use a professional, clear voice
      const voices = speechSynthesis.getVoices();
      const preferredVoice = voices.find(voice =>
        voice.name.includes('Google') ||
        voice.name.includes('Microsoft') ||
        voice.name.includes('Samantha') ||
        voice.name.includes('Karen') ||
        voice.name.includes('Daniel') ||
        voice.lang.includes('en-US')
      );

      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      // Add event listeners for better control
      utterance.onstart = () => {
        console.log('🗣️ Reading doctor message...');
      };

      utterance.onend = () => {
        console.log('✅ Finished reading doctor message');
      };

      utterance.onerror = (event) => {
        console.error('❌ Speech synthesis error:', event.error);
      };

      speechSynthesis.speak(utterance);
    }
  }

  // Stop any ongoing voice reading
  public stopVoiceReading() {
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
      console.log('🔇 Voice reading stopped');
    }
  }

  // Check if voice is currently speaking
  public isVoiceSpeaking(): boolean {
    if ('speechSynthesis' in window) {
      return speechSynthesis.speaking;
    }
    return false;
  }

  // Test notification system
  public testNotifications() {
    console.log('Testing notification system...');
    this.generateNotificationTone(600, 0.5);

    setTimeout(() => {
      if (this.isNotificationPermissionGranted) {
        new Notification('🔔 Notification Test', {
          body: 'Your notification system is working correctly!',
          icon: '/doctor-avatar.png'
        });
      }
    }, 600);
  }

  // Check if notifications are supported and enabled
  public getNotificationStatus() {
    return {
      browserSupport: 'Notification' in window,
      permission: this.isNotificationPermissionGranted,
      audioSupport: !!this.audioContext,
      speechSupport: 'speechSynthesis' in window
    };
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance();
