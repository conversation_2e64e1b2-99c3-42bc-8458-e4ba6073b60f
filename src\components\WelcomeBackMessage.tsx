import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { usePatient } from '../contexts/PatientContext';
import { Clock, MessageCircle, User, X, RefreshCw } from 'lucide-react';

const WelcomeBackMessage: React.FC = () => {
  const { user, getUserData, isNewUser } = useAuth();
  const { clearAllUserData } = usePatient();
  const [showWelcome, setShowWelcome] = useState(false);
  const [userData, setUserData] = useState<any>(null);

  useEffect(() => {
    // Reset welcome message state when user changes
    setShowWelcome(false);
    setUserData(null);

    // Only proceed if we have a user and it's NOT a new user
    if (!user || isNewUser) {
      return;
    }

    // Add a delay to ensure all authentication states are settled
    const timer = setTimeout(() => {
      const data = getUserData();

      // VERY STRICT CRITERIA for showing welcome message:
      // 1. Must have saved data
      // 2. Must have multiple sessions (totalSessions >= 2)
      // 3. Must have actual user activity (chat, complaint, or appointment)
      // 4. Last login must be from a previous session (not current)

      if (data &&
          data.totalSessions &&
          data.totalSessions >= 2 &&
          (
            (data.chatHistory && data.chatHistory.length > 0) ||
            (data.complaint && data.complaint.trim() !== '') ||
            (data.appointmentDate && data.appointmentDate.trim() !== '')
          )
      ) {
        setUserData(data);
        setShowWelcome(true);
      }
    }, 1000); // 1 second delay

    return () => clearTimeout(timer);
  }, [user, isNewUser, getUserData]);

  // Don't show anything if conditions aren't met
  if (!showWelcome || !userData) {
    return null;
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Recently';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-6 rounded-t-2xl relative">
          <button
            onClick={() => setShowWelcome(false)}
            className="absolute top-4 right-4 text-white hover:text-gray-200 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
              {user?.avatar ? (
                <img src={user.avatar} alt="Avatar" className="w-full h-full rounded-full object-cover" />
              ) : (
                <User className="w-6 h-6 text-white" />
              )}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">Welcome Back!</h2>
              <p className="text-white/80">{user?.username}</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          <div className="text-center">
            <p className="text-gray-600 mb-4">
              We found your previous session data. Here's what we remember about you:
            </p>
          </div>

          {/* Last Visit */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="w-5 h-5 text-indigo-600" />
              <h3 className="font-semibold text-gray-800">Last Visit</h3>
            </div>
            <p className="text-gray-600 text-sm">
              {formatDate(userData.lastLoginDate)}
            </p>
          </div>

          {/* Patient Info */}
          {userData.patientName && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <User className="w-5 h-5 text-indigo-600" />
                <h3 className="font-semibold text-gray-800">Patient Information</h3>
              </div>
              <p className="text-gray-600 text-sm">
                Name: {userData.patientName}
              </p>
              {userData.appointmentType && (
                <p className="text-gray-600 text-sm">
                  Last appointment type: {userData.appointmentType}
                </p>
              )}
            </div>
          )}

          {/* Chat History */}
          {userData.chatHistory && userData.chatHistory.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <MessageCircle className="w-5 h-5 text-indigo-600" />
                <h3 className="font-semibold text-gray-800">Previous Conversations</h3>
              </div>
              <p className="text-gray-600 text-sm mb-3">
                You have {userData.chatHistory.length} previous messages in your chat history.
              </p>

              {/* Show last few messages */}
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {userData.chatHistory.slice(-3).map((message: any, index: number) => (
                  <div key={index} className="text-xs">
                    <span className={`font-medium ${
                      message.role === 'user' ? 'text-blue-600' : 'text-green-600'
                    }`}>
                      {message.role === 'user' ? 'You' : 'Dr. Michael'}:
                    </span>
                    <span className="text-gray-600 ml-1">
                      {message.content.length > 100
                        ? message.content.substring(0, 100) + '...'
                        : message.content
                      }
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Previous Complaint */}
          {userData.complaint && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-2">Previous Complaint</h3>
              <p className="text-gray-600 text-sm">
                {userData.complaint.length > 150
                  ? userData.complaint.substring(0, 150) + '...'
                  : userData.complaint
                }
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col space-y-3 pt-4">
            <button
              onClick={() => setShowWelcome(false)}
              className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium"
            >
              Continue Where I Left Off
            </button>
            <button
              onClick={() => {
                clearAllUserData();
                setShowWelcome(false);
              }}
              className="w-full bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors font-medium flex items-center justify-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Start Fresh Session</span>
            </button>
          </div>

          <p className="text-xs text-gray-500 text-center">
            All your data is stored locally and securely on your device.
          </p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeBackMessage;
